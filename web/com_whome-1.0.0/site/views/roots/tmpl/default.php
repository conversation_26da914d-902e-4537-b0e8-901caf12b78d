<?php
/**
 * @version    CVS: 1.0.0
 * @package    Com_Whome
 * <AUTHOR> lo <<EMAIL>>
 * @copyright  版权所有 (C) 2015。所有权利保留。
 * @license    GNU 通用公共许可版本 2 或更高版本 ；请参阅 LICENSE.txt
 */
// No direct access
defined('_JEXEC') or die;

use \Joomla\CMS\HTML\HTMLHelper;
use \Joomla\CMS\Factory;
use \Joomla\CMS\Uri\Uri;
use \Joomla\CMS\Router\Route;
use \Joomla\CMS\Language\Text;

HTMLHelper::addIncludePath(JPATH_COMPONENT . '/helpers/html');
HTMLHelper::_('bootstrap.tooltip');
HTMLHelper::_('behavior.multiselect');
HTMLHelper::_('formbehavior.chosen', 'select');

$user       = Factory::getUser();
$userId     = $user->get('id');
$listOrder  = $this->state->get('list.ordering');
$listDirn   = $this->state->get('list.direction');
$canCreate  = $user->authorise('core.create', 'com_whome') && file_exists(JPATH_COMPONENT . DIRECTORY_SEPARATOR . 'models' . DIRECTORY_SEPARATOR . 'forms' . DIRECTORY_SEPARATOR . 'tableform.xml');
$canEdit    = $user->authorise('core.edit', 'com_whome') && file_exists(JPATH_COMPONENT . DIRECTORY_SEPARATOR . 'models' . DIRECTORY_SEPARATOR . 'forms' . DIRECTORY_SEPARATOR . 'tableform.xml');
$canCheckin = $user->authorise('core.manage', 'com_whome');
$canChange  = $user->authorise('core.edit.state', 'com_whome');
$canDelete  = $user->authorise('core.delete', 'com_whome');

if($user->id == 0)
{
	JError::raiseWarning( 403, JText::_( 'COM_WHOME_ERROR_MUST_LOGIN') );
	$joomlaLoginUrl = 'index.php?option=com_users&view=login';

	echo "<br><a href='".JRoute::_($joomlaLoginUrl)."'>".JText::_( 'COM_WHOME_LOG_IN')."</a><br>";
        return;
}


if(count($this->items) <= 0)
{
  echo("need upload picture");
  return;
}

$items = TopHelpersUtility::get_weather_items();

$key=$items[0]->key;
$city=TopHelpersUtility::$weather_cities[$items[0]->city];
$devicelog = WhomeHelpersWhome::get_devicelog();
$home_setting = TopHelpersUtility::get_home_site_settings();
$version = TopHelpersUtility::get_weema_package_version();
$accs = TopHelpersUtility::getMyAccount();
$suppress_auto_redirect = FloorHelpersFloor::getIfUserSuppressedAutoRedirect($user);
$disk_space = FloorHelpersFloor::getDiskSpace();
$db_size = FloorHelpersFloor::getDbSize();
$space = new StdClass();
// $space->disk_size = 0;
// $space->db_size = 0;
$space->disk_size = FloorHelpersFloor::HumanSize($disk_space);
$space->db_size = FloorHelpersFloor::HumanSize($db_size);
$space->disk_usage = round($db_size/$disk_space,2)." %";	
?>
<style>
.mymenu
{
	font-size: 18px;

}
.container_right {
    //float: right;
    width: 1280px;
    //border: 1px solid green;
    overflow: hidden; /* if you don't want #second to wrap below #first */
}
.container_left {

    float: left;
    width: 640px;
    //border: 1px solid #0099ff;
    padding: 0;
		//border-bottom: none;
}

.container_left_one{

    float: left;
    width: 320px;
    //border: 1px solid #0099ff;
    padding: 0;
		//border-bottom: none;
}
.container_left_two{

    float: right;
    width: 320px;
    //border: 1px solid #0099ff;
    padding: 0;
		//border-bottom: none;
}
.container_left_three{

    float: left;
    width: 160px;
    //border: 1px solid #0099ff;
    padding: 0;
		//border-bottom: none;
}
.container_left_four{

    float: left;
    width: 160px;
    //border: 1px solid #0099ff;
    padding: 0;
		//border-bottom: none;
}
.container_left_head {

    height: 60px;
    width: 100%;
    //border: 1px solid #0099ff;
    padding: 0;
		//border-bottom: none;
}

.container_left_data {

    height: 200px;
    width: 100%;
    //border: 1px solid #0099ff;
    padding: 0;
		//border-bottom: none;
}

.container_left_data_one {

    height: 400px;
    width: 100%;
    //border: 1px solid #0099ff;
    padding: 0;
		//border-bottom: none;
}

.left_head_css {

   text-align: center;
	 font-size:xx-large;
	 color:red;
}

.left_data_text_css {

   text-align: center;

}
.left_data_text_css_left {

   text-align: left;

}
.left_data_css{

	height: 100px;
	width: 100%;
}
.left_data_css_one{

	height: 300px;
	width: 100%;
}
.left_data_css_bottom{

	height: 50px;
	width: 100%;
}

.left_subdata_left_css {

  float: left;

	width: 240px;
}

.left_subdata_right_css {

  float: right;
	width: 240px;
}

.img_center {
	display: block;
   margin-left: auto;
   margin-right: auto;
   width: 50%;
	 height:100%;
}
.wrapper {
    margin: 0;
    padding: 0;
    width: 100%;
	  height: 100%;
    overflow: hidden; /* will contain if #first is longer than #second */
    //background-color: #F7F7F7;
}
body{
	width:1920px;
}
</style>
<form action="<?php echo htmlspecialchars(Uri::getInstance()->toString()); ?>" method="post"
      name="adminForm" id="adminForm">

	<div class="wrapper">
	<!-- <?php var_dump($home_setting) ?> -->

	<?php if ($home_setting->content->mode == 1): ?>
		<div class="container_left" style="padding-top:120px">
			<div class="container_left_head">
				<h1 class="left_head_css">能源資訊</h1>
				<h5 style="text-align:center;">v<?php echo($version); ?>，資料庫已用容量：<?php echo($space->db_size); ?>，剩餘空間：<?php echo($space->disk_size) ?></h5>
		</div>
        			<div class="container_left_one">
					<div class="container_left_data_one">
						<div class="left_data_css">
            						<img src="/media/com_whome/img/electric.png" class="img_center">
					</div>
						<div class="left_data_css_one">
							<div class="container_left_three">
            							<h3 class="left_data_text_css">累積用電量</h3>
								<h3 class="left_data_text_css">瓦特</h3>
            							<h3 class="left_data_text_css">電壓</h3>
								<h3 class="left_data_text_css">電流</h3>
								<h3 class="left_data_text_css">頻率</h3>
								<h3 class="left_data_text_css">功率因素</h3>
						</div>
							<div class="container_left_four">
            							<h3 class="left_data_text_css_left"><ins><span id="elec_kwh">?</span></ins>&nbsp&nbspKWh</h3>
								<h3 class="left_data_text_css_left"><ins><span id="elec_kw">?</span></ins>&nbsp&nbspKW</h3>
            							<h3 class="left_data_text_css_left"><ins><span id="elec_v">?</span></ins>&nbsp&nbspV</h3>
								<h3 class="left_data_text_css_left"><ins><span id="elec_a">?</span></ins>&nbsp&nbspA</h3>
								<h3 class="left_data_text_css_left"><ins><span id="elec_f">?</span></ins>&nbsp&nbspF</h3>
								<h3 class="left_data_text_css_left"><ins><span id="elec_pf">?</span></ins>&nbsp&nbspPF</h3>
						</div>
					</div>
						<div class="container_left_data"  style="display:none;">
							<div class="left_data_css">
								<img src="/media/com_whome/img/sun.png" class="img_center">
						</div>
							<div class="left_data_css"  style="display:none;">
								<h3 class="left_data_text_css">今日發電量</h3>
								<h3 class="left_data_text_css"><ins id="solar_power_today">N/A</ins> kWh</h3>
								<h3 class="left_data_text_css">累積發電量</h3>
								<h3 class="left_data_text_css"><ins id="solar_power_accumulate">N/A</ins> kWh</h3>
						</div>
					</div>
				</div>
		 	 </div>
				<div class="container_left_two" >
					<div class="left_data_css" style="display:none;">
						<img src="/media/com_whome/img/temp.png" class="img_center">
				</div>
					<div class="left_data_css" style="display:none;">
            					<h3 class="left_data_text_css"><span id="main_info">?</span></h3>
						<h3 class="left_data_text_css"><span id="main_temp">&nbsp&degC&nbsp,&nbsp</span><span id="main_humidity">&nbsp%</span></h3>
						
				</div>
					<div class="container_left_data">
						<div class="left_data_css">
							<img src="/media/com_whome/img/water.png" class="img_center">
					</div>
						<div class="left_data_css">
            						<h3 class="left_data_text_css">昨日總用水</h3>
							<h3 class="left_data_text_css"><ins id="total_water_usage">0</ins>&nbsp&nbsp度</h3>
					</div>
					<div class="left_data_css">
					<span id="weather_id"><img src="/media/com_whome/img/晴天.png" class="img_center"></span>
				</div>
				<div class="left_data_css">
										<h3 class="left_data_text_css">外面天氣</h3>
					<h3 class="left_data_text_css"><span id="out_desc">?</span></h3>
					<h3 class="left_data_text_css"><ins><span id="out_temp">?</span></ins>&nbsp&nbsp度</h3>

			</div>

				</div>
		 	 </div>
       	 </div>
	<?php endif; ?>
	<?php if ($home_setting->content->mode == 2): ?>

		<div class="container_left">
		<div class="container_left_head">
			<h1 class="left_head_css">能源資訊1</h1>
			<h5 style="text-align:center;">v<?php echo($version); ?>，資料庫已用容量：<?php echo($space->db_size); ?>，剩餘空間：<?php echo($space->disk_size) ?></h5>
		</div>
				<div class="container_left_one">
				<div class="container_left_data_one">
					<div class="left_data_css">
								<img src="/media/com_whome/img/electric.png" class="img_center">
				</div>
					<div class="left_data_css_one">
						<div class="container_left_three">
									<h3 class="left_data_text_css">累積用電量</h3>
							<h3 class="left_data_text_css">瓦特</h3>
									<h3 class="left_data_text_css">電壓</h3>
							<h3 class="left_data_text_css">電流</h3>
							<h3 class="left_data_text_css">頻率</h3>
							<h3 class="left_data_text_css">功率因素</h3>
					</div>
						<div class="container_left_four">
									<h3 class="left_data_text_css_left"><ins><span id="elec_kwh">?</span></ins>&nbsp&nbspKWh</h3>
							<h3 class="left_data_text_css_left"><ins><span id="elec_kw">?</span></ins>&nbsp&nbspKW</h3>
									<h3 class="left_data_text_css_left"><ins><span id="elec_v">?</span></ins>&nbsp&nbspV</h3>
							<h3 class="left_data_text_css_left"><ins><span id="elec_a">?</span></ins>&nbsp&nbspA</h3>
							<h3 class="left_data_text_css_left"><ins><span id="elec_f">?</span></ins>&nbsp&nbspF</h3>
							<h3 class="left_data_text_css_left"><ins><span id="elec_pf">?</span></ins>&nbsp&nbspPF</h3>
					</div>
				</div>
					<div class="container_left_data">
						<div class="left_data_css">
							<img src="/media/com_whome/img/sun.png" class="img_center">
					</div>
						<div class="left_data_css">
						<h3 class="left_data_text_css">今日發電量</h3>
							<h3 class="left_data_text_css"><ins id="solar_power_today">N/A</ins> kWh</h3>
							<h3 class="left_data_text_css">累積發電量</h3>
							<h3 class="left_data_text_css"><ins id="solar_power_accumulate">N/A</ins> kWh</h3>
					</div>
				</div>
			</div>
		</div>
			<div class="container_left_two">
				<div class="left_data_css">
					<img src="/media/com_whome/img/temp.png" class="img_center">
			</div>
				<div class="left_data_css">
							<h3 class="left_data_text_css"><span id="main_info">?</span></h3>
					<h3 class="left_data_text_css"><span id="main_temp">&nbsp&degC&nbsp,&nbsp</span><span id="main_humidity">&nbsp%</span></h3>
					
			</div>
			<div class="container_left_data">
				<div class="left_data_css">
				<table style="
    margin-top: 30px;
">
<tbody>
<tr>
<td style="width:33%"><img style="width:100%" src="/media/com_whome/img/water.png" class="img_center">		</td>
<td style="width:33%"><img style="width:100%" src="/media/com_whome/img/water.png" class="img_center">		</td>
<td style="width:33%"><img style="width:100%" src="/media/com_whome/img/water.png" class="img_center">		</td>
</tr>
<tr>
<td><h4 id="total_water_usage_name1" style="text-align:center"></h4>
</td>


<td><h4 id="total_water_usage_name2"  style="text-align:center"></h4>
</td><td><h4 id="total_water_usage_name3" style="text-align:center"></h4>
</td></tr>
<tr>
<td><h4 id="total_water_usage1" style="text-align:center"></h4>
</td>


<td><h4 id="total_water_usage2" style="text-align:center"></h4>
</td><td><h4 id="total_water_usage3" style="text-align:center"></h4>
</td></tr></tbody>
</table>
				</div>
				<div class="left_data_css" style="visibility:hidden">
							<h3 class="left_data_text_css">昨日總用水</h3>
					<h3 class="left_data_text_css"><ins id="total_water_usage">0</ins>&nbsp&nbsp度</h3>
				</div>
				<div class="left_data_css">
				<span id="weather_id"><img src="/media/com_whome/img/晴天.png" class="img_center"></span>
			</div>
			<div class="left_data_css">
									<h3 class="left_data_text_css">外面天氣</h3>
				<h3 class="left_data_text_css"><span id="out_desc">?</span></h3>
				<h3 class="left_data_text_css"><ins><span id="out_temp">?</span></ins>&nbsp&nbsp度</h3>

		</div>

			</div>
		</div>
		</div>
	<?php endif; ?>
	<?php if ($home_setting->content->mode == 0): ?>

        	<div class="container_left">
			<div class="container_left_head">
				<h1 class="left_head_css">能源資訊</h1>
				<h5 style="text-align:center;">v<?php echo($version); ?>，資料庫已用容量：<?php echo($space->db_size); ?>，剩餘空間：<?php echo($space->disk_size) ?></h5>
		</div>
        			<div class="container_left_one">
					<div class="container_left_data_one">
						<div class="left_data_css">
            						<img src="/media/com_whome/img/electric.png" class="img_center">
					</div>
						<div class="left_data_css_one">
							<div class="container_left_three">
            							<h3 class="left_data_text_css">累積用電量</h3>
								<h3 class="left_data_text_css">瓦特</h3>
            							<h3 class="left_data_text_css">電壓</h3>
								<h3 class="left_data_text_css">電流</h3>
								<h3 class="left_data_text_css">頻率</h3>
								<h3 class="left_data_text_css">功率因素</h3>
						</div>
							<div class="container_left_four">
            							<h3 class="left_data_text_css_left"><ins><span id="elec_kwh">?</span></ins>&nbsp&nbspKWh</h3>
								<h3 class="left_data_text_css_left"><ins><span id="elec_kw">?</span></ins>&nbsp&nbspKW</h3>
            							<h3 class="left_data_text_css_left"><ins><span id="elec_v">?</span></ins>&nbsp&nbspV</h3>
								<h3 class="left_data_text_css_left"><ins><span id="elec_a">?</span></ins>&nbsp&nbspA</h3>
								<h3 class="left_data_text_css_left"><ins><span id="elec_f">?</span></ins>&nbsp&nbspF</h3>
								<h3 class="left_data_text_css_left"><ins><span id="elec_pf">?</span></ins>&nbsp&nbspPF</h3>
						</div>
					</div>
						<div class="container_left_data">
							<div class="left_data_css">
								<img src="/media/com_whome/img/sun.png" class="img_center">
						</div>
							<div class="left_data_css">
							<h3 class="left_data_text_css">今日發電量</h3>
								<h3 class="left_data_text_css"><ins id="solar_power_today">N/A</ins> kWh</h3>
								<h3 class="left_data_text_css">累積發電量</h3>
								<h3 class="left_data_text_css"><ins id="solar_power_accumulate">N/A</ins> kWh</h3>
						</div>
					</div>
				</div>
		 	 </div>
				<div class="container_left_two">
					<div class="left_data_css">
						<img src="/media/com_whome/img/temp.png" class="img_center">
				</div>
					<div class="left_data_css">
            					<h3 class="left_data_text_css"><span id="main_info">?</span></h3>
						<h3 class="left_data_text_css"><span id="main_temp">&nbsp&degC&nbsp,&nbsp</span><span id="main_humidity">&nbsp%</span></h3>
						
				</div>
					<div class="container_left_data">
						<div class="left_data_css">
							<img src="/media/com_whome/img/water.png" class="img_center">
					</div>
						<div class="left_data_css">
            						<h3 class="left_data_text_css">昨日總用水</h3>
							<h3 class="left_data_text_css"><ins id="total_water_usage">0</ins>&nbsp&nbsp度</h3>
					</div>
					<div class="left_data_css">
					<span id="weather_id"><img src="/media/com_whome/img/晴天.png" class="img_center"></span>
				</div>
				<div class="left_data_css">
										<h3 class="left_data_text_css">外面天氣</h3>
					<h3 class="left_data_text_css"><span id="out_desc">?</span></h3>
					<h3 class="left_data_text_css"><ins><span id="out_temp">?</span></ins>&nbsp&nbsp度</h3>

			</div>

				</div>
		 	 </div>
       	 </div>
			<?php endif; ?>
      		<div id="container_right">
					<?php if(!empty($this->items[0]->picture)) :?>
		  <?php
		        list($width, $height, $type, $attr) = getimagesize($this->items[0]->picture);
		    ?>
		        <object width="1280" height="<?php echo $height;?>" data="<?php echo $this->items[0]->picture;?>"></object>

		      <?php endif;?>
        </div>
</div>

	<?php 
		$maxLogId = FloorHelpersFloor::findMaxLogId();
	?>

		<!-- <div id="test_audio" ><?php var_dump($maxLogId[0]->alarm_sound_file); ?></div> -->
		<audio id="myalarm">
		 
		  <!-- <source src="<?php echo($accs[0]->alarm_path."/".$accs[0]->alarm_file);?>" type="audio/mpeg"> -->
		  <?php 
			$alarm_sound_file = "";
		  if (count($maxLogId) > 0)
		  {
				$alarm_sound_file = $maxLogId[0]->alarm_sound_file;
		  } 
		  ?>
		  <source src="<?php echo("images/alarm_audios/".$alarm_sound_file);?>" type="audio/mpeg">
		Your browser does not support the audio element.
		</audio>


	<input type="hidden" name="task" value=""/>
	<input type="hidden" name="boxchecked" value="0"/>
	<input type="hidden" name="group" value="0"/>
	<input type="hidden" name="id" id="id" value="0"/>
	<input type="hidden" name="filter_order" value="<?php echo $listOrder; ?>"/>
	<input type="hidden" name="filter_order_Dir" value="<?php echo $listDirn; ?>"/>
	<?php echo HTMLHelper::_('form.token'); ?>
</form>

<?php if($canDelete) : ?>
<script type="text/javascript">

var timeout = 60000;
var weather_timeout = 60000*60;
var timeout_log_init = 3000;
var timeout_log = 1000;
var alarm_timeout = 4000;

var is_alarm = <?php echo FloorHelpersFloor::is_need_alarm();?>;

var weather_img=[

	'<img src="/media/com_whome/img/晴天.png" class="img_center">',
	'<img src="/media/com_whome/img/陰天.png" class="img_center">',
	'<img src="/media/com_whome/img/雨天.png" class="img_center">',
	'<img src="/media/com_whome/img/雷雨.png" class="img_center">',

];

var m_menuObj = [
<?php
	 $items = WhomeHelpersWhome::getMenus();
	 foreach($items as $i=>$item):?>
	{
		link:'<?php echo $item->link;?>',
		menu:'<?php echo JRoute::_($item->link, false, 2) ?>',
		alias:'<?php echo $item->alias;?>',

	},

	<?php endforeach;?>

];
<?php
			$max = 0;
			foreach($devicelog as $i => $item)
			{
					if($item->id > $max)
					{
						$max = $item->id;
					}
			}

	?>
var m_max = <?php echo $max;?>;

	jQuery(document).ready(function () {
		jQuery('.delete-button').click(deleteItem);
		//jQuery('#alarm1').click(alarmItem);
    //something();

		weatherFunc();
		statusFunc();
    playFunc();
    setTimeout(getLogStatus, timeout_log_init);

	});

	function deleteItem() {

		if (!confirm("<?php echo Text::_('COM_WHOME_DELETE_MESSAGE'); ?>")) {
			return false;
		}
	}

  function changeInfo(obj) {
		temp = jQuery("#main_temp").eq(0);
		temp.html("<ins>" + obj.temp + "</ins>&nbsp°C&nbsp,&nbsp");
		// temp = document.getElementById("main_temp");
		// temp.innerHTML = obj.temp/10 + "&nbsp;&nbsp;°C, " + obj.humidity + "&nbsp;&nbsp;%";	
		let humidity = jQuery("#main_humidity").eq(0);
		humidity.html("<ins>" + obj.humidity + "</ins>&nbsp%");


		let solar_power_today = jQuery('#solar_power_today');
		let solar_power_accumulate = jQuery('#solar_power_accumulate');

		solar_power_today.text(obj.solar_power_today);
		solar_power_accumulate.text(obj.solar_power_accumulate);
		// humidity = document.getElementById("main_humidity");
		// humidity.innerHTML = obj.humidity + "&nbsp;&nbsp;%";

		info = document.getElementById("main_info");
		info.innerHTML = obj.info;

		info = document.getElementById("elec_kwh");
		info.innerHTML = obj.elec_kwh;

		info = document.getElementById("elec_kw");
		info.innerHTML = obj.elec_kw;

		info = document.getElementById("elec_v");
		info.innerHTML = obj.elec_v;

		info = document.getElementById("elec_a");
		info.innerHTML = obj.elec_a;

		info = document.getElementById("elec_f");
		info.innerHTML = obj.elec_f;

		info = document.getElementById("elec_pf");
		info.innerHTML = obj.elec_pf;

		obj.total_water_usage = obj.total_water_usages[0].usage;
		console.log("usage", obj.total_water_usage);		
		info = document.getElementById("total_water_usage");
		if (info)
		{
			info.innerHTML = Math.round(obj.total_water_usage* 100) / 100;
		}
		for (let i = 1; i <= 3; i++) {
			var usage = document.getElementById("total_water_usage" + i);
			var usage_name = document.getElementById("total_water_usage_name" + i);
			var water_meter = obj.total_water_usages.find(e => e.no == i);
			if (usage)
			{
				usage.innerHTML =  Math.round(water_meter.usage* 100) / 100 + "&nbsp;度"; 
			}
			if (usage_name)
			{
				usage_name.innerHTML = water_meter.name ;
			}
		}
		
	}
	function showWeatherImg(value)
	{
		img = weather_img[0];

		switch(parseInt(value))
		{
			case 1:
			case 8:
			case 34:
			case 43:
			case 46:
			img = weather_img[0];

			break;
      case 2:
			case 3:
			case 5:
			case 6:
			case 7:
			case 13:
			case 44:
			case 45:
			img = weather_img[1];
			break;
			case 4:
			case 12:
			case 24:
			case 26:
			case 31:
			case 36:
			case 49:
			case 57:
			case 58:
			case 59:
			img = weather_img[2];
			break;
			case 17:
			case 18:
			img = weather_img[3];
			break;
			default:
			img = weather_img[0];
			break;
		}

    //console.log(value);
    //console.log(img);
		temp = document.getElementById("weather_id");
		temp.innerHTML = img;

	}
	function changeWeather(obj) {

		//console.log(obj.records.location);

    arr = obj.records.location;
		//console.log(arr[0].weatherElement);
		arr1 = arr[0].weatherElement;

    wx_value = '';
		minT_value = '';
		maxT_value = '';
		for (i = 0; i < arr1.length; i += 1) {
			if(arr1[i].elementName == "Wx")
			{
					wx_value = arr1[i].time[0].parameter.parameterName;
					//console.log(arr1[i].time[0].parameter.parameterValue);
					showWeatherImg(arr1[i].time[0].parameter.parameterValue);

			}
			else if(arr1[i].elementName == "MaxT")
			{
          maxT_value = arr1[i].time[0].parameter.parameterName;
			}
			else if(arr1[i].elementName == "MinT")
			{
          minT_value = arr1[i].time[0].parameter.parameterName;
			}

		}

		temp = document.getElementById("out_desc");
		temp.innerHTML = wx_value;

		temp = document.getElementById("out_temp");
		temp.innerHTML = minT_value+' ~ '+maxT_value;

	}

  function weatherFunc() {

		alprUrl = "<?php echo JRoute::_('index.php?option=com_floor&task=sroots.get_weather'); ?>";

    console.log(alprUrl);
    jQuery.ajax({
        url: alprUrl,

        success: function(response) {
            //console.log(response);
            //json = JSON.stringify(response)

            var arr = JSON.parse(response);
            //console.log(arr);
						changeWeather(arr);

            setTimeout(weatherFunc, weather_timeout);
        },
        error: function(response) {
            console.log("error");
            setTimeout(weatherFunc, timeout);

        }
    });

  }

	function is_match_1(building,this_building)
	{
			console.log("match "+building);
			var build = building.replace("filter_building=", "");

			first_char=build.charAt(0);

			 is_not_include = false;

			 if(first_char == "D")
			 {
					 is_not_include = true;
					 build = build.replace("D", "");
			 }

			 var res = build.split("-");
			 is_find = false;
			 is_not_find = true;
			 jQuery.each(res, function(index, value) {
				 if(is_not_include == false && value.localeCompare(this_building) == 0)
				 {
						 is_find = true;

						 //break;
				 }
				 if(is_not_include != false && value.localeCompare(this_building) == 0)
				 {
						 is_not_find = false;

						 //break;
				 }

		});

		ret = false;
		if(is_find != false || (is_not_include != false && is_not_find != false))
		{
			ret = true;
		}
		return ret;
	}
	function is_match(building,this_building)
	{
			var res = building.split("&");
			is_find = false;
			jQuery.each(res, function(index, value) {

			 if(value.indexOf('filter_building=') == 0)
			 {
					 if(is_find == false)
							 is_find = is_match_1(value,this_building);

					 //break;
			 }


			});

			return is_find;
	}
	function gotoItem(menu,obj) {

		form = document.getElementById('adminForm');

		form.group.value = obj.group;

		form.id.value = obj.floor;

		url = form.action.split("index.php");
		var res = menu.alias.split("-");
		if(res.length >= 2)
		{
				form.action = url[0]+"index.php/"+res[0]+'/'+menu.alias;
		}
		else
		{
				form.action = url[0]+"index.php/"+menu.alias;
		}
		console.log(form.action);

		//return;
		// Submit the form.
		if (typeof form.onsubmit == 'function') {
					form.onsubmit();
		}
		if (typeof form.fireEvent == "function") {
					form.fireEvent('onsubmit');
		}

		form.submit();

	}

	function alarmItemFunc(obj)
	{
			 for(i=0;i<m_menuObj.length;i++)
			 {
					 if(is_match(m_menuObj[i].link,obj.building) != false)
					 {
						 console.log(m_menuObj[i].menu+"&user-group="+obj.group+"&user-id="+obj.floor);

						 gotoItem(m_menuObj[i],obj);
						 //window.location.href = m_menuObj[i].menu+"&user-group="+obj.group+"&user-id="+obj.floor;

						 break;
					 }
			 }

	}
	function getLogStatus()
	{
		alprUrl = "<?php echo JRoute::_('index.php?option=com_floor&task=sroots.getLogStatus'); ?>";

		var logvar = [];

		var myvar = {id:logvar};

		//myvar = {};
		var jsonArray = JSON.stringify(myvar);

		//console.log(jsonArray);
		jQuery.ajax({
				url: alprUrl,
				headers: {
						"Content-Type": "application/json"
				},
				type: "POST",
				data: jsonArray,
				success: function(response) {

						//console.log(response);
						var obj = JSON.parse(response);

						<?php if (!$suppress_auto_redirect): ?>
						if(true || is_alarm != obj.is_need_alarm)
						{

							is_alarm = obj.is_need_alarm;

							var myalarm = jQuery("#myalarm");
							var myalarm_source = myalarm.children('source').eq(0);
							if(is_alarm == 1)
							{
								var path = '/images/alarm_audios/' + obj.alarm_sound_file;
								// console.log(myalarm_source.attr('src'), path);
								if (myalarm_source.attr('src') != path)
								{
									// console.log('change path');
									myalarm_source.attr('src', path);
									myalarm[0].pause();
									myalarm[0].currentTime = 0;  // 修正：current_time -> currentTime

									// 修正：在 load() 之前註冊事件監聽器，避免時序問題
									myalarm[0].addEventListener('canplaythrough', function() {
										myalarm[0].play();
									}, { once: true });

									myalarm[0].load();
									// console.log(myalarm_source.attr('src'));
									// playFunc();
								}
								else
								{
									if (is_alarm != obj.is_need_alarm)
									{
										// 修正：使用事件監聽器確保音效能正常播放
										myalarm[0].addEventListener('canplaythrough', function() {
											myalarm[0].play();
										}, { once: true });

										// 觸發載入檢查，如果已經載入完成會立即觸發事件
										myalarm[0].load();
									}
									// console.log('no change path');
								}

							}
							else
							{
								myalarm[0].pause();
								myalarm[0].currentTime = 0;  // 修正：current_time -> currentTime
							}
						}

						//console.log(obj);
						if(obj.max > m_max)
						{
								//console.log(obj);
								
    //   form.submit();
									
									alarmItemFunc(obj); 
									
						}
						else
						{
								//updateLog(obj.items);
								setTimeout(getLogStatus, timeout_log);
						}
						<?php endif; ?>
				},
				error: function(response) {
						console.log("error7");
						setTimeout(statusFunc, timeout_log*10);

				}
		});

	}

	function statusFunc() {

    alprUrl = "<?php echo JRoute::_('index.php?option=com_floor&task=sroots.get_main_info'); ?>";

    jQuery.ajax({
        url: alprUrl,
        headers: {
            "Content-Type": "application/json"
        },
        type: "POST",
        data: "",//jsonArray,
        success: function(response) {

            var arr = JSON.parse(response);
            console.log(arr);
						changeInfo(arr);

            setTimeout(statusFunc, timeout);
        },
        error: function(response) {
            console.log("error");
            setTimeout(statusFunc, timeout);

        }
    });

  }

	function playFunc()
	{
		   console.log(is_alarm);
		   if(is_alarm == 1)
			 {
	         document.getElementById("myalarm").play();

	         setTimeout(playFunc, alarm_timeout);
		   }
	 }


</script>
<?php endif; ?>
